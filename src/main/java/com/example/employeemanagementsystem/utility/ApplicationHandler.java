package com.example.employeemanagementsystem.utility;

import com.example.employeemanagementsystem.exception.UserObjectNotCreated;
import com.example.employeemanagementsystem.exception.UserNotFoundById;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

@RestControllerAdvice
public class ApplicationHandler {

    @ExceptionHandler
    public ResponseEntity<ErrorStructure<String>> userNotFoundById(UserNotFoundById e) {
        ErrorStructure<String> errorStructure = ErrorStructure.<String>builder()
                .type("User Not Found")
                .status(HttpStatus.NOT_FOUND.value())
                .message(e.getMessage())
                .build();
        return new ResponseEntity<>(errorStructure, HttpStatus.NOT_FOUND);
    }
}
