package com.example.movieticketbookingsystem.utility;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

@Component
public class RestResponseBuilder {

    public <T> ResponseEntity<ResponseStructure<T>> success(HttpStatus statusCode, String message, T data){
        return ResponseEntity.status(statusCode).body(ResponseStructure.<T>builder()
                .status(statusCode.value())
                .message(message)
                .data(data)
                .build());
    }

    public ResponseEntity<ErrorStructure> error(HttpStatus statusCode, String message){
        return ResponseEntity.status(statusCode).body(ErrorStructure.builder()
                .statusCode(statusCode.value())
                .message(message)
                .build());
    }

    // Additional convenience methods for common HTTP status codes
    public static <T> ResponseEntity<ResponseStructure<T>> created(String message, T data, HttpStatus status) {
        return ResponseEntity.status(HttpStatus.CREATED).body(ResponseStructure.<T>builder()
                .status(HttpStatus.CREATED.value())
                .message(message)
                .data(data)
                .build());
    }

    public static <T> ResponseEntity<ResponseStructure<T>> ok(String message, T data, HttpStatus status) {
        return ResponseEntity.ok(ResponseStructure.<T>builder()
                .status(HttpStatus.OK.value())
                .message(message)
                .data(data)
                .build());
    }
}

