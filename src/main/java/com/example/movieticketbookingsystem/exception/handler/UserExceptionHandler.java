package com.example.movieticketbookingsystem.exception.handler;

import com.example.movieticketbookingsystem.exception.UserExistByEmailException;
import com.example.movieticketbookingsystem.exception.UserNotFoundByEmailException;
import com.example.movieticketbookingsystem.exception.UserNotRegistered;
import com.example.movieticketbookingsystem.utility.ErrorStructure;
import com.example.movieticketbookingsystem.utility.RestResponseBuilder;
import jakarta.servlet.http.HttpServletRequest;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

/**
 * Exception handler specifically for user-related exceptions.
 * This handler provides detailed error responses for user management operations.
 */
@RestControllerAdvice
@AllArgsConstructor
@Slf4j
@Order(1)
public class UserExceptionHandler {

    private final RestResponseBuilder restResponseBuilder;

    /**
     * Handles UserNotFoundByEmailException when a user is not found by email.
     * 
     * @param ex the exception thrown
     * @param request the HTTP request
     * @return ResponseEntity with error details
     */
    @ExceptionHandler(UserNotFoundByEmailException.class)
    public ResponseEntity<ErrorStructure> handleUserNotFoundByEmail(
            UserNotFoundByEmailException ex, HttpServletRequest request) {
        
        log.error("User not found by email: {}", ex.getMessage());
        
        String errorMessage = ex.getMessage() != null && !ex.getMessage().isEmpty() 
            ? ex.getMessage() 
            : "User not found with the provided email address";
            
        return restResponseBuilder.error(HttpStatus.NOT_FOUND, errorMessage);
    }

    /**
     * Handles UserExistByEmailException when trying to create a user with existing email.
     * 
     * @param ex the exception thrown
     * @param request the HTTP request
     * @return ResponseEntity with error details
     */
    @ExceptionHandler(UserExistByEmailException.class)
    public ResponseEntity<ErrorStructure> handleUserExistByEmail(
            UserExistByEmailException ex, HttpServletRequest request) {
        
        log.warn("Attempt to create user with existing email: {}", ex.getMessage());
        
        String errorMessage = ex.getMessage() != null && !ex.getMessage().isEmpty() 
            ? ex.getMessage() 
            : "A user already exists with this email address";
            
        return restResponseBuilder.error(HttpStatus.CONFLICT, errorMessage);
    }

    /**
     * Handles UserNotRegistered exception when user is not properly registered.
     * 
     * @param ex the exception thrown
     * @param request the HTTP request
     * @return ResponseEntity with error details
     */
    @ExceptionHandler(UserNotRegistered.class)
    public ResponseEntity<ErrorStructure> handleUserNotRegistered(
            UserNotRegistered ex, HttpServletRequest request) {
        
        log.error("User not registered: {}", ex.getMessage());
        
        String errorMessage = ex.getMessage() != null && !ex.getMessage().isEmpty() 
            ? ex.getMessage() 
            : "User is not registered in the system. Please complete registration first";
            
        return restResponseBuilder.error(
                HttpStatus.UNAUTHORIZED,
                errorMessage,
                request.getRequestURI()
        );
    }
}
