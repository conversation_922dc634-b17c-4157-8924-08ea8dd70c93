package com.example.product_management_api.controller;

import com.example.product_management_api.dtos.ProductDto;
import com.example.product_management_api.utility.ResponseStructure;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/products")
public class ProductController {

    @PostMapping
    public ResponseEntity<ResponseStructure<ProductDto>> addProduct(){

    }
}
